/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert

import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.TipUtil.Companion.TYPE_ROLE_NAME
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.R
import com.soundrecorder.playback.convert.IConvertManager
import com.soundrecorder.playback.databinding.FragmentPlaybackConvertBinding
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.util.SummarySupportManager

class PlaybackConvertFragment : Fragment() {

    companion object {
        const val TAG = "PlaybackConvertFragment"
    }

    var mConvertManagerImpl: IConvertManager? = null
    private var mBinding: FragmentPlaybackConvertBinding? = null
    private var paddingBottom: Int = 0

    private val mViewModel: PlaybackActivityViewModel by lazy {
        ViewModelProvider(requireParentFragment())[PlaybackActivityViewModel::class.java]
    }
    private val mConvertViewModel: PlaybackConvertViewModel by lazy {
        ViewModelProvider(requireParentFragment())[PlaybackConvertViewModel::class.java]
    }

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        DebugUtil.i(TAG, "onCreate $this , savedInstanceState $savedInstanceState")
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        DebugUtil.i(TAG, "onCreateView")
        mBinding = FragmentPlaybackConvertBinding.bind(inflater.inflate(R.layout.fragment_playback_convert, container, false))
        mConvertManagerImpl = (requireParentFragment() as? PlaybackContainerFragment)?.mConvertManagerImpl
        (requireParentFragment() as? PlaybackContainerFragment)?.let {
            (it.convertViewContainer?.parent as? ViewGroup)?.removeAllViews()
            val layoutParams = FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            mBinding?.convertContainer?.addView(it.convertViewContainer, layoutParams)
        }
        initViewModelObserver()
        return mBinding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mBinding?.fragmentConvertRootView?.updatePadding(bottom = paddingBottom)
    }

    private fun initViewModelObserver() {
        mConvertViewModel.isSpeakerRoleShowing.observe(viewLifecycleOwner) { isSpeakerRoleShowing ->
            DebugUtil.i(TAG, "isShowConvertRole changeed $isSpeakerRoleShowing")
            mConvertManagerImpl?.roleControl(isSpeakerRoleShowing)
        }

        //need observe activity
        mViewModel.playerController.currentTimeMillis.observe(viewLifecycleOwner) { currentTime ->
            mConvertManagerImpl?.update(currentTime)
        }

        mViewModel.playName.observe(viewLifecycleOwner) {
            mConvertManagerImpl?.updatePlayName()
        }

        mViewModel.mIsPageStopScroll.observe(viewLifecycleOwner) { isShowRoleNameTips ->
            mConvertManagerImpl?.getConvertViewController()?.setNeedShowRoleName(isShowRoleNameTips)
            if (isShowRoleNameTips) {
                mConvertManagerImpl?.addSpeakerTipTask { isOnConvertWhenViewPager2IDLE() }
            } else {
                mConvertManagerImpl?.removeSpeakerTipTask()
            }
        }

        // 恢复滚动位置
        mConvertViewModel.visibleItemLocation.observe(viewLifecycleOwner) {
            val layoutManager = mConvertManagerImpl?.getConvertViewController()?.getCustomLinearLayoutManager()
            layoutManager?.let {
                mConvertViewModel.restoreScrollPosition(it)
            }
        }
        // 摘要按钮
        mViewModel.mSummaryStatus.observe(viewLifecycleOwner) {
            mViewModel.addShowSummaryEvent(
                SummaryStaticUtil.EVENT_FROM_CONVERT,
                SummarySupportManager.supportRecordSummary.value,
                it == RecordSummaryManager.SUMMARY_STATE_CLIENT_END)
        }
    }

    private fun isOnConvertWhenViewPager2IDLE(): Boolean {
        val playbackFragment = parentFragment as? PlaybackContainerFragment ?: return false
        val viewPager2IDLE = playbackFragment.isViewPager2IDLE()
        val onConvert = playbackFragment.isOnConvert()
        val fullConvertPage = isFullConvertPage()
        DebugUtil.d(TAG, "isOnConvertWhenViewPager2IDLE idle:$viewPager2IDLE onConvert:$onConvert fullPage:$fullConvertPage")
        return viewPager2IDLE && onConvert && fullConvertPage
    }

    private fun isFullConvertPage(): Boolean {
        val xy = intArrayOf(-1, -1)
        mBinding?.fragmentConvertRootView?.getLocationOnScreen(xy)
        DebugUtil.d(TAG, "isFullConvertPage,[${xy[0]},${xy[1]}]")
        if (xy[0] > 0 && ScreenUtil.getWindowType(resources.configuration) != WindowType.SMALL) {
            val maxWidth = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.sub_window_parent_max_width)
            val parentPercentDefault = browseFileApi?.getParentPercentDefault() ?: 0f
            val defaultWidth = ScreenUtil.getRealScreenWidthContainSystemBars() * parentPercentDefault + NumberConstant.NUM_F0_5
            val parentViewWidth = maxWidth.coerceAtMost(defaultWidth.toInt())
            DebugUtil.d(TAG, "isFullConvertPage, parentViewWidth = [$parentViewWidth]")
            // 中大屏减去父子级左侧列表宽度
            return (xy[0] - parentViewWidth) == 0
        }
        return xy[0] == 0
    }

    fun switchSpeakerRoleState() {
        if (!isAdded) {
            return
        }
        mConvertViewModel.swithSpeakerRoleState()
    }

    fun convertSearch() {
        if (!isAdded) {
            return
        }
        // 记录滚动位置
        mConvertManagerImpl?.getConvertViewController()?.saveScrollPosition("convert_fragment")

        mConvertViewModel.also {
            // 统计
            it.clickConvertSearchCount++
            // 进入转文本搜索界面
            it.mIsInConvertSearch.value = true
        }
        BuryingPoint.addClickContentSearch()
    }

    fun stopScroll() {
        val hasShow = TipUtil.hasShowTip(TYPE_ROLE_NAME)
        if (!hasShow) {
            mConvertManagerImpl?.stopScroll()
        }
    }

    override fun onDestroyView() {
        mConvertManagerImpl?.releaseView()
        super.onDestroyView()
    }

    override fun onDestroy() {
        DebugUtil.d(TAG, "onDestroy")
        /* 讲话人编辑次数埋点 */
        if (RecorderUserActionKt.sEditCount > 0) {
            BuryingPoint.addRecordSpeakerEdit(
                mViewModel.recordId.toString(),
                RecorderUserAction.VALUE_EDIT_FROM, RecorderUserActionKt.sIsAppliedAll, RecorderUserActionKt.sEditCount)
            /*结束埋点 讲话人编辑次数 恢复默认值*/
            RecorderUserActionKt.sEditCount = 0
        }
        mConvertManagerImpl?.cacheWindowShowing()
        mConvertManagerImpl?.release()
        super.onDestroy()
    }

    override fun onConfigurationChanged(p0: Configuration) {
        super.onConfigurationChanged(p0)
        mConvertManagerImpl?.onConfigurationChanged(p0)
    }

    fun updatePaddingBottom(paddingBottom: Int) {
        this.paddingBottom = paddingBottom
        mBinding?.fragmentConvertRootView?.updatePadding(bottom = paddingBottom)
    }
}